// Core Node.js imports
import path from 'path';
import fs from 'fs/promises';
import delay from 'delay';

// Internal imports
import { AgentManager } from '../agent';
import { SayTool } from '../types/type';
import { HandleError, PushToolResult, RemoveClosingTag, ToolUse, ReportToolAction } from '../types/message';
import { getReadablePath } from '@/util/path';
import { fileExistsAtPath } from '@/util/fs';
import { ASSISTANT_NAMESPACE } from '@/util/const';

/**
 * Tool for performing search and replace operations on files
 * Supports regex and case-sensitive/insensitive matching
 */

/**
 * Validates required parameters for search and replace operation
 */
async function validateParams(
  agent: AgentManager,
  relPath: string | undefined,
  search: string | undefined,
  replace: string | undefined,
  pushToolResult: PushToolResult
): Promise<boolean> {
  if (!relPath) {
    agent.consecutiveMistakeCount++;
    pushToolResult(await agent.sayAndCreateMissingParamError('search_and_replace', 'path'));
    return false;
  }

  if (!search) {
    agent.consecutiveMistakeCount++;
    pushToolResult(await agent.sayAndCreateMissingParamError('search_and_replace', 'search'));
    return false;
  }

  if (replace === undefined) {
    agent.consecutiveMistakeCount++;
    pushToolResult(await agent.sayAndCreateMissingParamError('search_and_replace', 'replace'));
    return false;
  }

  return true;
}

/**
 * Performs search and replace operations on a file
 * @param agent - Cline instance
 * @param block - Tool use parameters
 * @param handleError - Function to handle errors
 * @param pushToolResult - Function to push tool results
 * @param removeClosingTag - Function to remove closing tags
 */
export async function searchAndReplaceTool(
  agent: AgentManager,
  block: ToolUse,
  handleError: HandleError,
  pushToolResult: PushToolResult,
  removeClosingTag: RemoveClosingTag,
  reportToolAction: ReportToolAction
): Promise<void> {
  // Extract and validate parameters
  const relPath: string | undefined = block.params.path;
  const search: string | undefined = block.params.search;
  const replace: string | undefined = block.params.replace;
  const startLine: number | undefined = block.params.start_line ? parseInt(block.params.start_line, 10) : undefined;
  const endLine: number | undefined = block.params.end_line ? parseInt(block.params.end_line, 10) : undefined;

  try {
    const sharedMessageProps: SayTool = {
      tool: 'editFile',
      path: getReadablePath(agent.cwd, relPath),
      content: removeClosingTag('replace', replace),
      tool_version: 'v2'
    };
    // Handle partial tool use
    if (block.partial) {
      agent.removeLastPartialMessageIfExistsWithType('ask', 'tool');
      await agent.say('tool', JSON.stringify(sharedMessageProps), block.partial).catch(() => {});
      return;
    }

    // Validate required parameters
    if (!(await validateParams(agent, relPath, search, replace, pushToolResult))) {
      return;
    }

    // At this point we know relPath, search and replace are defined
    const validRelPath = relPath as string;
    const validSearch = search as string;
    const validReplace = replace as string;

    agent.logger.reportUserAction({
      key: 'agent_tools_request',
      type: 'search_and_replace'
    });
    const startToolTime = Date.now();

    const absolutePath = path.resolve(agent.cwd, validRelPath);
    const fileExists = await fileExistsAtPath(absolutePath);

    if (!fileExists) {
      agent.consecutiveMistakeCount++;
      const formattedError = `File does not exist at path: ${absolutePath}\nThe specified file could not be found. Please verify the file path and try again.`;
      await agent.say('error', formattedError);
      pushToolResult(formattedError);
      return;
    }

    // Reset consecutive mistakes since all validations passed
    agent.consecutiveMistakeCount = 0;

    // Read and process file content
    let fileContent: string;
    try {
      fileContent = await fs.readFile(absolutePath, 'utf-8');
    } catch (error) {
      agent.consecutiveMistakeCount++;
      const errorMessage = `Error reading file: ${absolutePath}\nFailed to read the file content: ${
        error instanceof Error ? error.message : String(error)
      }\nPlease verify file permissions and try again.`;
      await agent.say('error', errorMessage);
      pushToolResult(errorMessage);
      return;
    }

    // Create search pattern and perform replacement
    const flags = 'g';
    const searchPattern = new RegExp(escapeRegExp(validSearch), flags);

    let newContent: string;
    if (startLine !== undefined || endLine !== undefined) {
      // Handle line-specific replacement
      const lines = fileContent.split('\n');
      const start = Math.max((startLine ?? 1) - 1, 0);
      const end = Math.min((endLine ?? lines.length) - 1, lines.length - 1);

      // Get content before and after target section
      const beforeLines = lines.slice(0, start);
      const afterLines = lines.slice(end + 1);

      // Get and modify target section
      const targetContent = lines.slice(start, end + 1).join('\n');
      const modifiedContent = targetContent.replace(searchPattern, validReplace);
      const modifiedLines = modifiedContent.split('\n');

      // Reconstruct full content
      newContent = [...beforeLines, ...modifiedLines, ...afterLines].join('\n');
    } else {
      // Global replacement
      newContent = fileContent.replace(searchPattern, validReplace);
    }
    agent.removeLastPartialMessageIfExistsWithType('ask', 'tool');
    await agent.say('tool', JSON.stringify({ ...sharedMessageProps, content: newContent }), block.partial);
    await delay(1000); // wait for diff view to update
    let generationCall = agent.trace?.generation({
      name: 'tool_call',
      input: {
        startLine,
        endLine,
        replace: validReplace,
        search: search,
        path: validRelPath,
        newContent
      },
      metadata: {
        name: block.name
      }
    });

    const { data } = await agent.messenger.request('assistant/agent/writeToFile', {
      path: validRelPath,
      content: newContent,
      newFile: false
    });
    reportToolAction(Date.now() - startToolTime, {
      contentLength: newContent.length,
      noModified: !!data?.noModified,
      type: data?.type
    });

    if (data?.noModified) {
      pushToolResult(`No changes needed for '${relPath}'`);
      return;
    }
    if (data?.type === 'success') {
      const resultMessage = [
        `The updated content has been successfully saved to ${validRelPath.toPosix()}. Here is the full, updated content of the file:\n\n`,
        `<final_file_content path="${validRelPath.toPosix()}">\n${newContent}\n</final_file_content>\n\n`,
        `Please note:\n`,
        `1. You do not need to re-write the file with these changes, as they have already been applied.\n`,
        `2. Proceed with the task using the updated file content as the new baseline.\n`,
        `3. If the user's edits have addressed part of the task or changed the requirements, adjust your approach accordingly.`
      ].join('');
      pushToolResult(resultMessage);
    } else {
      pushToolResult(data?.content || '');
    }
    generationCall?.end({
      output: { type: data?.type, content: data?.content }
    });
    agent.logger.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-tool',
      millis: Date.now() - startToolTime,
      extra4: data?.type === 'success' ? 'success' : 'error',
      extra6: block.name
    });

    await agent.saveCheckpoint();
  } catch (error: any) {
    await handleError('search and replace', error, 'search_and_replace');
  }
}

/**
 * Escapes special regex characters in a string
 * @param input String to escape regex characters in
 * @returns Escaped string safe for regex pattern matching
 */
function escapeRegExp(input: string): string {
  return input.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
